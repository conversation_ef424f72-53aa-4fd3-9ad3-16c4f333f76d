import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/info',
    },
    {
      path: '/home',
      name: 'home',
      component: HomeView,
      meta: { guestAllowed: true },
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/auth/LoginView.vue'),
      meta: { guestAllowed: true },
    },
    {
      path: '/info',
      name: 'info',
      component: () => import('../views/info/InfoView.vue'),
      meta: { guestAllowed: true },
    },
    {
      path: '/info/:id',
      name: 'info-detail',
      component: () => import('../views/info/InfoView.vue'),
      meta: { guestAllowed: true },
    },
    {
      path: '/study',
      name: 'study',
      component: () => import('../views/study/StudyView.vue'),
      meta: { guestAllowed: true },
    },
    {
      path: '/exam',
      name: 'exam',
      component: () => import('../views/exam/ExamView.vue'),
      meta: { guestAllowed: true },
    },
    {
      path: '/exam/:id',
      name: 'exam-detail',
      component: () => import('../views/exam/ExamDetailView.vue'),
      meta: { guestAllowed: true },
    },
    {
      path: '/exam/:id/take/:recordId',
      name: 'exam-take',
      component: () => import('../views/exam/ExamTakeView.vue'),
      meta: { requiresAuth: true, permission: 'exam:take' },
    },
    {
      path: '/exam/:id/result/:recordId',
      name: 'exam-result',
      component: () => import('../views/exam/ExamResultView.vue'),
      meta: { requiresAuth: true, permission: 'exam:read' },
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/profile/ProfileView.vue'),
      meta: { guestAllowed: true },
      redirect: '/profile/info',
      children: [
        {
          path: 'info',
          name: 'profile-info',
          component: () => import('../views/profile/ProfileView.vue'),
          meta: { guestAllowed: true },
        },
        {
          path: 'certificates',
          name: 'profile-certificates',
          component: () => import('../views/profile/ProfileView.vue'),
          meta: { guestAllowed: true },
        },
      ],
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
      meta: { guestAllowed: true },
    },
    // 404 页面
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('../views/common/NotFoundView.vue'),
      meta: { guestAllowed: true },
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // 如果是访客允许的页面，直接通行
  if (to.meta.guestAllowed) {
    // 如果已登录用户访问登录页，重定向到首页
    if (to.name === 'login' && authStore.isAuthenticated) {
      // 处理登录后的重定向
      const redirect = to.query.redirect as string
      if (redirect && redirect !== '/login') {
        next(redirect)
      } else {
        next('/info')
      }
      return
    }
    next()
    return
  }

  // 需要认证的页面
  if (!authStore.isAuthenticated) {
    // 未登录，重定向到登录页，并保存当前路径用于登录后恢复
    next({ name: 'login', query: { redirect: to.fullPath } })
    return
  }

  // 检查权限
  if (to.meta.permission && !authStore.hasPermission(to.meta.permission as string)) {
    // 权限不足，重定向到信息中心
    next('/info')
    return
  }

  next()
})

export default router
